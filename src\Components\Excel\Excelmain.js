import {
  createUniver,
  defaultTheme,
  LocaleType,
  merge,
} from "@univerjs/presets";
import { UniverSheetsCorePreset } from "@univerjs/presets/preset-sheets-core";
import UniverPresetSheetsCoreEnUS from "@univerjs/presets/preset-sheets-core/locales/en-US";

import "./style.css";
import "@univerjs/presets/lib/styles/preset-sheets-core.css";

import { BooleanNumber, SheetTypes } from "@univerjs/core";
import { LocaleType as CoreLocaleType } from "@univerjs/core";
import { useEffect, useRef, useState } from "react";
import { ToastContainer, Zoom } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

// Convert A-Z column letters to number index (0-based)
export const letterToColumn = (letter) => {
  let col = 0;
  for (let i = 0; i < letter.length; i++) {
    col = col * 26 + (letter.charCodeAt(i) - 65 + 1);
  }
  return col - 1;
};

// Convert number index to A-Z column letter
export const columnToLetter = (col) => {
  let letter = "";
  while (col >= 0) {
    letter = String.fromCharCode((col % 26) + 65) + letter;
    col = Math.floor(col / 26) - 1;
  }
  return letter;
};

// Convert "A1" -> { rowIndex, colIndex }
export const cellRefToIndices = (cellRef) => {
  const match = cellRef.match(/([A-Z]+)(\d+)/);
  if (!match) return null;
  const [, colLetter, rowNumber] = match;
  const rowIndex = parseInt(rowNumber, 10) - 1;
  const colIndex = letterToColumn(colLetter);
  return { rowIndex, colIndex };
};

// API → Univer Matrix format
export const transformApiToMatrix = (
  apiData,
  isFromResponseSubmitted = false
) => {
  const cellData = {};
  let maxRow = 0;
  let maxCol = 0;

  if (apiData?.excelData) {
    Object?.entries(apiData?.excelData).forEach(([cell, value]) => {
      const { rowIndex, colIndex } = cellRefToIndices(cell);
      if (!cellData[rowIndex]) cellData[rowIndex] = {};

      // Check if the value is a formula (starts with =)
      if (typeof value === "string" && value.startsWith("=")) {
        // Store as formula to ensure recalculation
        // When loading from responseSubmitted, don't include calculated values
        // so formulas will recalculate fresh with new question data
        cellData[rowIndex][colIndex] = { f: value };
      } else {
        // Store as regular value
        cellData[rowIndex][colIndex] = { v: value };
      }

      maxRow = Math.max(maxRow, rowIndex);
      maxCol = Math.max(maxCol, colIndex);
    });
  }

  if (Array?.isArray(apiData?.maskedCells)) {
    apiData?.maskedCells.forEach((cellRef) => {
      const { rowIndex, colIndex } = cellRefToIndices(cellRef);
      if (!cellData[rowIndex]) cellData[rowIndex] = {};
      if (!cellData[rowIndex][colIndex]) cellData[rowIndex][colIndex] = {};

      // Always apply yellow highlighting to all masked cells with left text alignment
      cellData[rowIndex][colIndex].s = {
        bg: { rgb: "#FFFF00" },
        ht: 1, // 1 = left alignment
      };

      // When highlightAllMaskedCells is true:
      // Clear the cell value (make it empty) to hide any data
      if (apiData?.highlightAllMaskedCells) {
        // Clear the cell value to hide any data
        cellData[rowIndex][colIndex].v = "";
      }
    });
  }

  return {
    id: "workbook-01",
    name: "universheet",
    sheetOrder: ["sheet-01"],
    sheets: {
      "sheet-01": {
        type: SheetTypes.GRID,
        id: "sheet-01",
        name: "Sheet 1",
        tabColor: "blue",
        cellData,
        hidden: BooleanNumber.FALSE,
        rowCount: Math.max(maxRow + 1, 16),
        columnCount: Math.max(maxCol + 1, 16),
        defaultRowHeight: 28,
        defaultColumnWidth: 93,
        rowHeader: { width: 46, hidden: BooleanNumber.FALSE },
        columnHeader: { height: 20, hidden: BooleanNumber.FALSE },
        selections: ["A1"],
        zoomRatio: 1,
        scrollTop: 0,
        scrollLeft: 0,
        showGridlines: 1,
        status: 1,
        hideRow: [],
        hideColumn: [],
        pluginMeta: {},
        rightToLeft: BooleanNumber.FALSE,
      },
    },
  };
};

// Matrix → API format
export const transformMatrixToApi = (cellData) => {
  const apiExcelData = {};
  Object.entries(cellData).forEach(([rowIndex, row]) => {
    Object.entries(row).forEach(([colIndex, cell]) => {
      const colLetter = columnToLetter(parseInt(colIndex));
      const rowNumber = parseInt(rowIndex) + 1;

      // Prefer formula over value if both exist, otherwise use value
      if (cell.f) {
        apiExcelData[`${colLetter}${rowNumber}`] = String(cell.f);
      } else if (cell.v !== undefined && cell.v !== null) {
        apiExcelData[`${colLetter}${rowNumber}`] = String(cell.v);
      }
    });
  });
  return { excelData: apiExcelData };
};

// Main Component
// ... (imports remain unchanged)

/**
 * Excel Sheets Component
 * @param {Object} props - Component props
 * @param {Object} props.cellsData - Excel cell data
 * @param {Array} props.maskedCells - Array of masked cell references (e.g. ["A1", "B2"])
 * @param {Function} props.SetExcelApiData - Function to set Excel API data
 * @param {Function} props.SetCellsFilled - Function to set if cells are filled
 * @param {string} props.excelID - ID for the Excel component
 * @param {Object} [props.responseSubmitted] - Previously submitted response data
 * @param {boolean} [props.highlightAllMaskedCells=false] - When true, highlights all masked cells even if they don't have data
 */
export default function ExcelSheets({
  cellsData,
  maskedCells,
  SetExcelApiData,
  SetCellsFilled,
  excelID,
  responseSubmitted,
  highlightAllMaskedCells = false,
}) {
  const [workbookData, setWorkbookData] = useState(null);
  const univerCreatedRef = useRef(false);
  const univerAPIRef = useRef(null);
  const workbookDataRef = useRef(null);

  useEffect(() => {
    // When moving to a new question, we want to start fresh with the new question's data
    // If responseSubmitted exists, it means we're loading previous answers, but we should
    // use the current question's base data (cellsData) and only preserve user formulas
    let dataToUse;

    if (responseSubmitted && cellsData) {
      // Merge: use current question's base data but preserve user-entered formulas from responseSubmitted
      dataToUse = { ...cellsData };

      // Only preserve formulas (values starting with =) from responseSubmitted
      Object.entries(responseSubmitted).forEach(([cell, value]) => {
        if (typeof value === "string" && value.startsWith("=")) {
          dataToUse[cell] = value; // Preserve user formulas
        }
        // For non-formulas, use the current question's data (cellsData)
      });
    } else {
      // Use responseSubmitted if no current cellsData, or cellsData if no responseSubmitted
      dataToUse = responseSubmitted || cellsData;
    }

    const matrix = transformApiToMatrix({
      excelData: dataToUse,
      maskedCells,
      highlightAllMaskedCells,
    });

    const initialWorkbook = {
      ...matrix,
      locale: CoreLocaleType.EN_US,
      appVersion: "3.0.0-alpha",
    };

    setWorkbookData(initialWorkbook);
    workbookDataRef.current = initialWorkbook;
    univerCreatedRef.current = false;
  }, [responseSubmitted, cellsData, maskedCells, highlightAllMaskedCells]);

  useEffect(() => {
    if (!workbookData || univerCreatedRef.current) return;

    const { univerAPI } = createUniver({
      locale: LocaleType.EN_US,
      locales: {
        [LocaleType.EN_US]: merge({}, UniverPresetSheetsCoreEnUS),
      },
      theme: defaultTheme,
      presets: [UniverSheetsCorePreset()],
    });

    univerAPIRef.current = univerAPI;
    univerAPI.createWorkbook(workbookData);

    // Force formula recalculation to ensure fresh calculations for new question data
    const formulaEngine = univerAPI.getFormula();
    if (formulaEngine) {
      formulaEngine.executeCalculation();
    }

    const initialCellData = workbookData.sheets["sheet-01"].cellData;
    SetExcelApiData(transformMatrixToApi(initialCellData));

    // When highlightAllMaskedCells is true, we consider all cells as not filled
    // since we're hiding any data that might be in them
    if (highlightAllMaskedCells) {
      SetCellsFilled(false);
    } else {
      // Normal behavior - check if any masked cells have data
      let anyMaskedFilled = false;
      for (const cellRef of maskedCells) {
        const { rowIndex, colIndex } = cellRefToIndices(cellRef);
        const cell = initialCellData?.[rowIndex]?.[colIndex];
        if (cell?.v !== undefined && cell.v !== null && cell.v !== "") {
          anyMaskedFilled = true;
          break;
        }
      }
      SetCellsFilled(anyMaskedFilled);
    }

    // 🔒 Lock all cells except masked ones
    const restrictPermissionOnCells = async () => {
      const book = univerAPI?.getActiveWorkbook();
      const sheet = book.getActiveSheet();

      const bookId = book.getId();
      const sheetId = sheet.getSheetId();
      const permission = book.getPermission();

      const allCells = [];
      for (let row = 1; row <= 100; row++) {
        for (let col = 0; col < 26; col++) {
          const cell = `${String.fromCharCode(65 + col)}${row}`;
          allCells.push(cell);
        }
      }

      const cellsToLock = allCells.filter(
        (cell) => !maskedCells.includes(cell)
      );
      const ranges = cellsToLock.map((cell) => sheet.getRange(cell));

      const { permissionId, ruleId } = await permission.addRangeBaseProtection(
        bookId,
        sheetId,
        ranges
      );

      const rangeProtectionPermissionEditPoint =
        permission.permissionPointsDefinition
          .RangeProtectionPermissionEditPoint;

      permission.rangeRuleChangedAfterAuth$.subscribe((currentPermissionId) => {
        if (currentPermissionId === permissionId) {
          permission.setRangeProtectionPermissionPoint(
            bookId,
            sheetId,
            permissionId,
            rangeProtectionPermissionEditPoint,
            false
          );
        }
      });
    };

    restrictPermissionOnCells();

    // 🟡 Keep the existing edit logic for immediate UI updates
    univerAPI.addEvent(univerAPI.Event.SheetEditChanging, (params) => {
      const newValue = params?.value?._data?.body?.dataStream;
      const row = params?.row;
      const column = params?.column;

      if (newValue === undefined || row === undefined || column === undefined)
        return;

      const trimmedValue = String(newValue).trim();
      const refWorkbook = workbookDataRef.current;
      const cellData = refWorkbook?.sheets?.["sheet-01"]?.cellData || {};
      const updatedCellData = { ...cellData };

      if (!updatedCellData[row]) updatedCellData[row] = {};
      const existingCell = updatedCellData[row][column] || {};

      // Check if this is a masked cell
      const isMaskedCell = maskedCells.some((cellRef) => {
        const { rowIndex, colIndex } = cellRefToIndices(cellRef);
        return rowIndex === row && colIndex === column;
      });

      if (isMaskedCell) {
        // For masked cells
        if (highlightAllMaskedCells) {
          // When highlightAllMaskedCells is true:
          // 1. Always apply yellow highlighting
          // 2. Keep the cell value empty to hide any data
          updatedCellData[row][column] = {
            ...existingCell,
            v: "", // Keep the cell empty regardless of user input
            t: 1,
            s: {
              bg: { rgb: "#FFFF00" }, // Always highlight
              ht: 1, // 1 = left alignment
            },
          };
        } else {
          // When highlightAllMaskedCells is false:
          // 1. Always apply yellow highlighting
          // 2. Store formula if it's a formula, otherwise store value
          const cellUpdate = {
            ...existingCell,
            t: 1,
            s: {
              bg: { rgb: "#FFFF00" }, // Always highlight
              ht: 1, // 1 = left alignment
            },
          };

          // Check if it's a formula
          if (trimmedValue.startsWith("=")) {
            cellUpdate.f = trimmedValue; // Store as formula
            // Remove old value to ensure recalculation
            delete cellUpdate.v;
          } else {
            cellUpdate.v = trimmedValue; // Store as value
            // Remove old formula if exists
            delete cellUpdate.f;
          }

          updatedCellData[row][column] = cellUpdate;
        }
      } else {
        // For non-masked cells, normal behavior
        const cellUpdate = {
          ...existingCell,
          t: 1,
          s: existingCell?.s,
        };

        // Check if it's a formula
        if (trimmedValue.startsWith("=")) {
          cellUpdate.f = trimmedValue; // Store as formula
          // Remove old value to ensure recalculation
          delete cellUpdate.v;
        } else {
          cellUpdate.v = trimmedValue; // Store as value
          // Remove old formula if exists
          delete cellUpdate.f;
        }

        updatedCellData[row][column] = cellUpdate;
      }

      // Only update workbookDataRef temporarily for UI responsiveness
      // The final state will be updated by SheetValueChanged event
      workbookDataRef.current = {
        ...refWorkbook,
        sheets: {
          ...refWorkbook.sheets,
          ["sheet-01"]: {
            ...refWorkbook.sheets["sheet-01"],
            cellData: updatedCellData,
          },
        },
      };
    });

    // 🔥 NEW: Listen to SheetValueChanged to capture calculated formula results
    univerAPI.addEvent(univerAPI.Event.SheetValueChanged, () => {
      // Get the current workbook and sheet data from Univer
      const book = univerAPI?.getActiveWorkbook();
      const sheet = book?.getActiveSheet();

      if (!sheet) return;

      const refWorkbook = workbookDataRef.current;
      const existingCellData =
        refWorkbook?.sheets?.["sheet-01"]?.cellData || {};
      const updatedCellData = {};

      // Get the maximum range to check (we'll check a reasonable range)
      const maxRow =
        Math.max(
          Object.keys(existingCellData)
            .map((r) => parseInt(r))
            .filter((r) => !isNaN(r))
            .concat([15])
        )[0] || 15;
      const maxCol = 15; // Check up to column P (16 columns)

      // Process all cells in a reasonable range to capture calculated values
      for (let rowNum = 0; rowNum <= maxRow; rowNum++) {
        for (let colNum = 0; colNum <= maxCol; colNum++) {
          // Get the cell reference
          const cellRef = `${columnToLetter(colNum)}${rowNum + 1}`;

          // Get both the calculated value and formula from Univer using the Range API
          let calculatedValue = "";
          let formula = "";
          try {
            const range = sheet.getRange(rowNum, colNum);
            calculatedValue = range.getValue() || "";
            formula = range.getFormula() || "";
          } catch (error) {
            // If there's an error getting the value, use empty string
            calculatedValue = "";
            formula = "";
          }

          // Only process cells that have data or are masked cells
          const existingCell = existingCellData?.[rowNum]?.[colNum];
          const isMaskedCell = maskedCells.includes(cellRef);
          const hasData =
            existingCell ||
            calculatedValue !== "" ||
            formula !== "" ||
            isMaskedCell;

          if (hasData) {
            if (!updatedCellData[rowNum]) updatedCellData[rowNum] = {};

            if (isMaskedCell) {
              // For masked cells, preserve styling and handle value based on highlightAllMaskedCells
              if (highlightAllMaskedCells) {
                updatedCellData[rowNum][colNum] = {
                  ...existingCell,
                  v: "", // Keep empty when highlighting all masked cells
                  t: 1,
                  s: {
                    bg: { rgb: "#FFFF00" },
                    ht: 1, // 1 = left alignment
                  },
                };
              } else {
                const cellUpdate = {
                  ...existingCell,
                  t: 1,
                  s: {
                    bg: { rgb: "#FFFF00" },
                    ht: 1, // 1 = left alignment
                  },
                };

                // Preserve formula if it exists, otherwise use calculated value
                if (formula) {
                  cellUpdate.f = formula;
                  cellUpdate.v = calculatedValue; // Also store calculated value
                } else {
                  cellUpdate.v = calculatedValue;
                  // Remove old formula if no formula exists
                  delete cellUpdate.f;
                }

                updatedCellData[rowNum][colNum] = cellUpdate;
              }
            } else {
              // For non-masked cells
              const cellUpdate = {
                ...existingCell,
                t: 1,
                s: existingCell?.s,
              };

              // Preserve formula if it exists, otherwise use calculated value
              if (formula) {
                cellUpdate.f = formula;
                cellUpdate.v = calculatedValue; // Also store calculated value
              } else {
                cellUpdate.v = calculatedValue;
                // Remove old formula if no formula exists
                delete cellUpdate.f;
              }

              updatedCellData[rowNum][colNum] = cellUpdate;
            }
          }
        }
      }

      // Update the workbook data reference with calculated values
      workbookDataRef.current = {
        ...refWorkbook,
        sheets: {
          ...refWorkbook.sheets,
          ["sheet-01"]: {
            ...refWorkbook.sheets["sheet-01"],
            cellData: updatedCellData,
          },
        },
      };

      // Update the API data with calculated values
      SetExcelApiData(transformMatrixToApi(updatedCellData));

      // Update cells filled status
      if (highlightAllMaskedCells) {
        SetCellsFilled(false);
      } else {
        let anyFilled = false;
        for (const cellRef of maskedCells) {
          const { rowIndex, colIndex } = cellRefToIndices(cellRef);
          const cell = updatedCellData?.[rowIndex]?.[colIndex];
          if (cell?.v !== undefined && cell.v !== null && cell.v !== "") {
            anyFilled = true;
            break;
          }
        }
        SetCellsFilled(anyFilled);
      }
    });

    univerCreatedRef.current = true;

    return () => {
      univerAPIRef.current?.dispose?.();
      univerAPIRef.current = null;
      univerCreatedRef.current = false;
    };
  }, [workbookData, excelID]);

  return (
    <div>
      <div className="univer-container" id={excelID} />
      <ToastContainer transition={Zoom} />
    </div>
  );
}
